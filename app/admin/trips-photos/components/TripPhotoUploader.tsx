'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Upload, X, Check, Loader2, FileText, Cloud, AlertCircle, Image as ImageIcon } from 'lucide-react';
import { TripPhotoDetails } from '@/types/trip-photos';
import GooglePhotosAuth from '@/components/admin/GooglePhotosAuth';
import { isGooglePhotosAlbumLink, extractGooglePhotosAlbumId } from '@/lib/google-photos-oauth';

interface TripPhotoUploaderProps {
  tripPhotoDetails: TripPhotoDetails;
}

interface UploadItem {
  id: string;
  file: File;
  originalUrl: string; // URL created from the File object for preview
  status: 'pending' | 'processing' | 'uploading' | 'success' | 'error';
  progress: number;
  errorMessage?: string;
  driveUrl?: string;
  imageUrl?: string; // URL to the watermarked image (can be base64 data URL)
  removing?: boolean; // For animation
}

export default function TripPhotoUploader({ tripPhotoDetails }: TripPhotoUploaderProps) {
  const [uploadItems, setUploadItems] = useState<UploadItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [folderAccessValid, setFolderAccessValid] = useState<boolean | null>(null);
  const [isFolderValidating, setIsFolderValidating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [currentWatermarkPreview, setCurrentWatermarkPreview] = useState<string | null>(null);
  const [googlePhotosAuth, setGooglePhotosAuth] = useState<{
    isAuthenticated: boolean;
    accessToken?: string;
  }>({ isAuthenticated: false });

  // Cleanup object URLs on unmount
  useEffect(() => {
    // No need to revoke object URLs anymore since we're using data URLs
    return () => {};
  }, []);

  // Validate folder access when component loads
  useEffect(() => {
    if (tripPhotoDetails.google_drive_link) {
      validateFolderAccess();
    }
  }, [tripPhotoDetails.google_drive_link]);

  const validateFolderAccess = async () => {
    if (!tripPhotoDetails.google_drive_link) {
      setFolderAccessValid(false);
      return;
    }

    setIsFolderValidating(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/trips-photos/validate-folder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ folderUrl: tripPhotoDetails.google_drive_link }),
      });

      const data = await response.json();
      setFolderAccessValid(data.valid);
      
      if (!data.valid) {
        setError('The service account does not have access to the specified Google Drive folder. Please share the folder with the service account email: <EMAIL>');
      }
    } catch (err) {
      setError('Failed to validate folder access. Please check the Google Drive folder URL.');
      setFolderAccessValid(false);
    } finally {
      setIsFolderValidating(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    setError(null);
    
    // Convert FileList to array and process each file
    const newFiles = Array.from(files);
    
    // Filter out non-image files
    const invalidFiles = newFiles.filter(file => !file.type.startsWith('image/'));
    if (invalidFiles.length > 0) {
      setError(`${invalidFiles.length} file(s) were skipped because they are not images.`);
    }
    
    const validFiles = newFiles.filter(file => file.type.startsWith('image/'));
    
    // Create upload items for each valid file
    const newUploadItems: UploadItem[] = [];
    
    validFiles.forEach(file => {
      // Create a FileReader instance
      const reader = new FileReader();
      
      // Create a new upload item with a temporary ID
      const newItem: UploadItem = {
        id: Math.random().toString(36).substring(2, 9),
        file,
        originalUrl: '',
        status: 'pending',
        progress: 0,
      };
      
      // When the FileReader has loaded the image
      reader.onload = () => {
        // Update the upload item with the data URL
        newItem.originalUrl = reader.result as string;
        
        // Add the item to the upload items state
        setUploadItems(prev => [...prev, newItem]);
      };
      
      // Read the file as a data URL
      reader.readAsDataURL(file);
      
      // Add the item to our array
      newUploadItems.push(newItem);
    });
    
    // Reset the input value to allow selecting the same files again
    e.target.value = '';
  };

  const processAndUpload = async (item: UploadItem) => {
    // Update item status to processing
    setUploadItems(prev =>
      prev.map(i => i.id === item.id ? { ...i, status: 'processing', progress: 10 } : i)
    );

    try {
      const isGooglePhotos = isGooglePhotosAlbumLink(tripPhotoDetails.google_drive_link);

      if (isGooglePhotos && googlePhotosAuth.isAuthenticated && googlePhotosAuth.accessToken) {
        // Step 1: Watermark the image first
        const watermarkFormData = new FormData();
        watermarkFormData.append('image', item.file);
        watermarkFormData.append('tripPhotoDetailsId', tripPhotoDetails.id);

        const watermarkResponse = await fetch('/api/admin/trips-photos/watermark', {
          method: 'POST',
          body: watermarkFormData,
        });

        if (!watermarkResponse.ok) {
          const errorData = await watermarkResponse.json();
          throw new Error(errorData.message || 'Failed to watermark image');
        }

        const watermarkData = await watermarkResponse.json();
        setCurrentWatermarkPreview(watermarkData.imageUrl);

        // Update progress to uploading
        setUploadItems(prev =>
          prev.map(i => i.id === item.id ? { ...i, status: 'uploading', progress: 50 } : i)
        );

        // Step 2: Upload to Google Photos using OAuth
        const uploadFormData = new FormData();
        uploadFormData.append('file', item.file);
        uploadFormData.append('tripPhotoId', tripPhotoDetails.id);
        uploadFormData.append('accessToken', googlePhotosAuth.accessToken);

        const uploadResponse = await fetch('/api/admin/trips-photos/upload-to-photos-oauth', {
          method: 'POST',
          body: uploadFormData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.error || 'Failed to upload to Google Photos');
        }

        const uploadData = await uploadResponse.json();

        // Update progress and set success
        setUploadItems(prev =>
          prev.map(i => i.id === item.id ? {
            ...i,
            progress: 100,
            status: 'success',
            imageUrl: watermarkData.imageUrl,
            driveUrl: uploadData.photoUrl
          } : i)
        );
      } else {
        // Original watermark-only flow for Google Drive
        const formData = new FormData();
        formData.append('image', item.file);
        formData.append('tripPhotoDetailsId', tripPhotoDetails.id);

        const watermarkResponse = await fetch('/api/admin/trips-photos/watermark', {
          method: 'POST',
          body: formData,
        });

        if (!watermarkResponse.ok) {
          const errorData = await watermarkResponse.json();
          throw new Error(errorData.message || 'Failed to watermark image');
        }

        const watermarkData = await watermarkResponse.json();

        // Set the watermarked image preview
        setCurrentWatermarkPreview(watermarkData.imageUrl);

        // Update progress and set the imageUrl (now can be base64)
        setUploadItems(prev =>
          prev.map(i => i.id === item.id ? {
            ...i,
            progress: 100,
            status: 'success',
            imageUrl: watermarkData.imageUrl,
            driveUrl: watermarkData.driveUrl || undefined
          } : i)
        );
      }
      
      // Schedule removal with animation after 5 seconds
      setTimeout(() => {
        setUploadItems(prev => 
          prev.map(i => i.id === item.id ? { ...i, removing: true } : i)
        );
        
        // Actually remove after animation completes (500ms)
        setTimeout(() => {
          setUploadItems(prev => {
            const filteredItems = prev.filter(i => i.id !== item.id);
            
            // Check if there are no more items left, and if so, clear the watermark preview
            if (filteredItems.length === 0) {
              setCurrentWatermarkPreview(null);
            }
            
            return filteredItems;
          });
        }, 500);
      }, 5000);
      
    } catch (err: any) {
      setUploadItems(prev => 
        prev.map(i => i.id === item.id ? { 
          ...i, 
          status: 'error', 
          errorMessage: err.message || 'An error occurred during processing' 
        } : i)
      );
    }
  };

  const handleUploadAll = async () => {
    if (uploadItems.length === 0 || isUploading) return;
    
    setIsUploading(true);
    setError(null);
    
    // Get only pending items
    const pendingItems = uploadItems.filter(item => item.status === 'pending');
    
    // Process items one by one
    for (const item of pendingItems) {
      await processAndUpload(item);
    }
    
    setIsUploading(false);
  };

  const removeItem = (id: string) => {
    // First set the removing flag for animation
    setUploadItems(prev => 
      prev.map(item => item.id === id ? { ...item, removing: true } : item)
    );
    
    // Then actually remove after animation completes
    setTimeout(() => {
      setUploadItems(prev => prev.filter(item => item.id !== id));
    }, 500);
  };

  const clearCompleted = () => {
    // First set the removing flag for all completed items
    setUploadItems(prev => 
      prev.map(item => 
        (item.status === 'success' || item.status === 'error') 
          ? { ...item, removing: true } 
          : item
      )
    );
    
    // Then actually remove after animation completes
    setTimeout(() => {
      setUploadItems(prev => {
        const remaining = prev.filter(item => 
          item.status !== 'success' && item.status !== 'error'
        );
        
        // If there are no more items or only pending items left, clear the watermark preview
        if (remaining.length === 0 || remaining.every(item => item.status === 'pending')) {
          setCurrentWatermarkPreview(null);
        }
        
        return remaining;
      });
    }, 500);
  };

  const handleGooglePhotosAuthChange = (isAuthenticated: boolean, accessToken?: string) => {
    setGooglePhotosAuth({ isAuthenticated, accessToken });
  };

  return (
    <div className="space-y-6">
      {/* Google Photos Authentication for Google Photos albums */}
      {isGooglePhotosAlbumLink(tripPhotoDetails.google_drive_link) && (
        <GooglePhotosAuth
          onAuthChange={handleGooglePhotosAuthChange}
          albumId={tripPhotoDetails.google_drive_link ? extractGooglePhotosAlbumId(tripPhotoDetails.google_drive_link) || undefined : undefined}
        />
      )}

      {/* Remove Watermark Preview Modal and replace with inline card */}
      {currentWatermarkPreview && (
        <div className="bg-white border rounded-lg overflow-hidden shadow-md">
          <div className="px-4 py-3 border-b flex justify-between items-center bg-gray-50">
            <h3 className="text-lg font-medium text-gray-900">Latest Watermarked Image</h3>
          </div>
          <div className="relative aspect-video overflow-hidden">
            <Image
              src={currentWatermarkPreview}
              alt="Watermarked Preview"
              fill
              className="object-contain"
              onError={(e) => {
                console.error("Watermark preview failed to load");
                e.currentTarget.src = '/images/placeholder.jpg';
              }}
            />
          </div>
          <div className="px-4 py-3 text-sm text-gray-600">
            This preview shows how your image will appear with the watermark applied.
            Uploaded images will be saved to Google Drive with this watermark.
          </div>
        </div>
      )}

      {!folderAccessValid && tripPhotoDetails.google_drive_link && !isGooglePhotosAlbumLink(tripPhotoDetails.google_drive_link) && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p className="font-medium">Google Drive folder access issue</p>
            <p className="text-sm">
              Please make sure you have shared the Google Drive folder with the service account email: 
              <span className="font-medium"> <EMAIL></span>
            </p>
          </div>
        </div>
      )}

      {isGooglePhotosAlbumLink(tripPhotoDetails.google_drive_link) && !googlePhotosAuth.isAuthenticated && (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md flex items-start">
          <ImageIcon className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p className="font-medium">Google Photos Album Detected</p>
            <p className="text-sm">
              Please authenticate with Google Photos above to enable uploading images to this album.
              Your personal Google account will be used for uploads.
            </p>
          </div>
        </div>
      )}

      <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6">
        <div className="text-center">
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <div className="mt-2">
            <label
              htmlFor={`file-upload-${tripPhotoDetails.id}`}
              className="relative cursor-pointer rounded-md bg-white font-medium text-primary-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 hover:text-primary-500"
            >
              <span>Select images to upload to {tripPhotoDetails.trip_name}</span>
              <input
                id={`file-upload-${tripPhotoDetails.id}`}
                name="file-upload"
                type="file"
                className="sr-only"
                accept="image/*"
                multiple
                onChange={handleFileChange}
              />
            </label>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            PNG, JPG, GIF up to 10MB. Images will be watermarked and uploaded automatically.
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {!tripPhotoDetails.google_drive_link && uploadItems.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>
            <p className="font-medium">No storage location configured</p>
            <p className="text-sm">
              Add a Google Drive folder URL or Google Photos album link to the trip photos details to enable uploading.
            </p>
          </div>
        </div>
      )}

      {uploadItems.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">
              Upload Queue ({uploadItems.length} {uploadItems.length === 1 ? 'image' : 'images'})
            </h3>
            <div className="flex space-x-3">
              {uploadItems.some(item => item.status === 'success' || item.status === 'error') && (
                <button
                  type="button"
                  onClick={clearCompleted}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Clear Completed
                </button>
              )}
              <button
                type="button"
                onClick={handleUploadAll}
                disabled={isUploading || uploadItems.every(item => item.status !== 'pending')}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Process & Upload All
                  </>
                )}
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {uploadItems.map((item) => (
              <div 
                key={item.id} 
                className={`relative border rounded-lg overflow-hidden bg-white transition-all duration-500 ${item.removing ? 'opacity-0 scale-95' : 'opacity-100 scale-100'}`}
              >
                <div className="relative aspect-video bg-gray-100">
                  {/* Always show the original image for preview */}
                  {item.originalUrl && (
                    <div className="absolute inset-0 w-full h-full">
                      <Image
                        src={item.originalUrl}
                        alt={`Preview of ${item.file.name}`}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          console.error("Image failed to load:", item.file.name);
                          e.currentTarget.src = '/images/placeholder.jpg';
                        }}
                      />
                    </div>
                  )}
                  
                  {/* Status overlay */}
                  {item.status !== 'pending' && (
                    <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                      {item.status === 'processing' && (
                        <div className="text-white flex flex-col items-center">
                          <Loader2 className="h-8 w-8 animate-spin" />
                          <span className="mt-2 text-sm font-medium">Watermarking...</span>
                        </div>
                      )}
                      {item.status === 'uploading' && (
                        <div className="text-white flex flex-col items-center">
                          <Loader2 className="h-8 w-8 animate-spin" />
                          <span className="mt-2 text-sm font-medium">Uploading...</span>
                        </div>
                      )}
                      {item.status === 'success' && (
                        <div className="text-white flex flex-col items-center">
                          <Check className="h-8 w-8" />
                          <span className="mt-2 text-sm font-medium">Complete</span>
                        </div>
                      )}
                      {item.status === 'error' && (
                        <div className="text-white flex flex-col items-center">
                          <AlertCircle className="h-8 w-8" />
                          <span className="mt-2 text-sm font-medium">Error</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="p-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm truncate flex-1" title={item.file.name}>
                      {item.file.name}
                    </span>
                    <button
                      onClick={() => removeItem(item.id)}
                      className="ml-2 text-gray-500 hover:text-red-500"
                      disabled={item.status === 'processing' || item.status === 'uploading'}
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  
                  {/* Progress bar */}
                  {(item.status === 'processing' || item.status === 'uploading') && (
                    <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                      <div 
                        className="bg-primary-600 h-1.5 rounded-full" 
                        style={{ width: `${item.progress}%` }}
                      ></div>
                    </div>
                  )}
                  
                  {/* Error message */}
                  {item.status === 'error' && item.errorMessage && (
                    <p className="text-xs text-red-600 mt-1">{item.errorMessage}</p>
                  )}
                  
                  {/* Storage info */}
                  {item.status === 'success' && (
                    <div className="inline-flex items-center px-2 py-1 mt-2 text-xs font-medium rounded-md text-green-700 bg-green-50">
                      <Cloud className="h-3 w-3 mr-1" />
                      {isGooglePhotosAlbumLink(tripPhotoDetails.google_drive_link) ? 'Saved to Google Photos' : 'Saved to Google Drive'}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 